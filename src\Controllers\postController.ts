import { Request, Response , NextFunction } from 'express';
import { Post } from '../entities/Post';
import { AppDataSource } from "../config/db";
import { sendResponse  } from '../utils/sendResponse';
import { ObjectId } from "mongodb";
const postRepository = AppDataSource.getRepository(Post);

export const addPost = async(req : Request , res : Response , next : NextFunction) => { 
    try { 
            const post = postRepository.create(req.body);
            await postRepository.save(post );
            sendResponse(res ,200 , { 
                message : "Post added successfully", 
                data : post 
            }) ;
    }
    catch(err) { 
        next(err);
    }
}

export const UpdatePost = async(req : Request , res : Response , next : NextFunction) =>  {  
    try {
        const PostId = new ObjectId(req.params.id) ; 
        const isExist = await postRepository.findOneBy({ _id : PostId }) ; 
        if(!isExist) { 
            sendResponse(res , 404 , { message : "Post not found" }) ;
        }
           await postRepository.update({ _id: PostId }, req.body); 
            sendResponse(res, 200, { message: "Post updated successfully" });

    }
    catch(err) { 
        next(err) ;

    }
} 

export const GetAllPosts  = async(_req : Request , res : Response , next : NextFunction) =>  {  
    
    try { 
        const posts = await postRepository.find() ; 
        sendResponse(res , 200 , { message : "Posts fetched successfully" , data : posts }) ;
    }
    catch(err) { 
        next(err) ;
    }

}

export const DeletePost = async(req : Request , res : Response , next : NextFunction) =>  {  
    try { 
        const PostId = new ObjectId(req.params.id) ; 
        const isExist = await postRepository.findOneBy({ _id : PostId }) ; 
        if(!isExist) { 
            sendResponse(res , 404 , { message : "Post not found" }) ;
        }
        await postRepository.delete(PostId) ;
        sendResponse(res , 200 , {message : "post deleted sucessfully"}) ;
    }
    catch(err)  {
        next(err) ;
    }

}
