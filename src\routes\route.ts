import express from 'express';
const route = express.Router(); 
import type { Request, Response } from 'express';
import {addPost , UpdatePost , GetAllPosts , DeletePost} from '../Controllers/postController';
route.get('/hello', (_req : Request, res : Response ) => {
    res.send('Hello World!');
  });

route.get('/GetAllPost' , GetAllPosts);
route.post('/addPost' ,addPost ) ;
route.put('/updatePost/:id' ,UpdatePost );
route.delete('/deletePost/:id' ,DeletePost );


export default route;