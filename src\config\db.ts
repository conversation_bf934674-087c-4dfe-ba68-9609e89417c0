import "reflect-metadata";
import { DataSource } from "typeorm";
import dotenv from "dotenv";
import {Post} from '../entities/Post' ; 

dotenv.config();

export const AppDataSource = new DataSource({
  type: "mongodb",
  url: process.env.MONGO_URI as string,
  database: "testdb", 
  synchronize: true,
  logging: false,
  entities: [Post],
});

export const connectDB = async (): Promise<void> => {
  try {
    await AppDataSource.initialize();
    console.log(`MongoDB Connected: ${AppDataSource.options.database}`);
  } catch (error) {
    console.error("MongoDB connection error:", error);
    process.exit(1); // Exit on failure
  }
};
