import express from "express";
import dotenv from "dotenv";
import route from "./routes/route";
import { connectDB } from "./config/db";

dotenv.config();

const app = express();
const port: number = Number(process.env.PORT) || 3005;


app.use(express.json());


app.use("/api", route);

// Connect to DB and start server
connectDB().then(() => {
  app.listen(port, () => {
    console.log(`🚀 Server running on http://localhost:${port}`);
  });
});
